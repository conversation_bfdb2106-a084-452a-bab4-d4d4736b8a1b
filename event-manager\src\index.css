/* Fonts are now loaded via preload in index.html for better performance */
@import './styles/searchComponents.css';

:root {
  font-family: 'Space Grotesk', 'Poppins', system-ui, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #050505;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  --primary: #6e44ff;
  --primary-rgb: 110, 68, 255;
  --secondary: #ff44e3;
  --secondary-rgb: 255, 68, 227;
  --accent: #44ffd2;
  --accent-rgb: 68, 255, 210;
  --dark-bg: #050505;
  --dark-surface: #111111;
  --text-primary: rgba(255, 255, 255, 0.87);
  --text-secondary: rgba(255, 255, 255, 0.6);
}

a {
  font-weight: 500;
  color: var(--primary);
  text-decoration: inherit;
  transition: color 0.3s ease;
}
a:hover {
  color: var(--secondary);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  overflow-x: hidden;
  background-color: var(--dark-bg);
  color: var(--text-primary);
  min-height: 100vh;
  width: 100%;
}

body {
  margin: 0;
  position: relative;
  min-width: 320px;
  min-height: 100vh;
}

.smooth-scroll {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.scroll-container {
  position: relative;
  width: 100%;
}

.section {
  position: relative;
  width: 100%;
  overflow: hidden;
}

/* Removed problematic overlay that was causing black overlay on home page navigation */
/* .overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
  mix-blend-mode: difference;
} */

h1, h2, h3, h4, h5, h6 {
  font-family: 'Space Grotesk', sans-serif;
  line-height: 1.1;
  font-weight: 700;
  margin: 0;
}

h1 {
  font-size: clamp(3rem, 8vw, 6rem);
  background: linear-gradient(to right, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 0.5em;
}

h2 {
  font-size: clamp(2rem, 6vw, 4rem);
  margin-bottom: 0.5em;
}

p {
  font-family: 'Poppins', sans-serif;
  margin-bottom: 1.5em;
  font-size: clamp(1rem, 1.2vw, 1.2rem);
  line-height: 1.6;
  color: var(--text-secondary);
}

.btn {
  border-radius: 4px;
  border: 1px solid var(--primary);
  padding: 0.8em 2em;
  font-size: 1em;
  font-weight: 600;
  font-family: inherit;
  background-color: transparent;
  color: var(--primary);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: var(--primary);
  z-index: -1;
  transition: width 0.3s ease;
}

.btn:hover {
  color: #000;
}

.btn:hover:before {
  width: 100%;
}

.btn-primary {
  background-color: var(--primary);
  color: #000;
  border: none;
}

.btn-primary:before {
  background: var(--secondary);
}

button:focus,
button:focus-visible {
  outline: 2px solid var(--accent);
  outline-offset: 2px;
}

.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.z-10 {
  z-index: 10;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.py-section {
  padding: 10rem 0;
}

.gradient-text {
  background: linear-gradient(to right, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.appear {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.appear.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Animation classes */
.fade-in {
  animation: fadeIn 1s ease forwards;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-up {
  animation: slideUp 1s ease forwards;
}

@keyframes slideUp {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .py-section {
    padding: 6rem 0;
  }
}
