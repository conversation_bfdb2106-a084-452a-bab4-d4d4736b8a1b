#root {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

.app {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: var(--dark-bg);
}

/* Loader */
.loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--dark-bg);
  z-index: 9999;
}

.loader h1 {
  margin-bottom: 2rem;
}

.loader-bar {
  width: 0;
  height: 4px;
  background: linear-gradient(to right, var(--primary), var(--secondary));
  border-radius: 2px;
  max-width: 300px;
}

/* Custom cursor */
.cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--accent);
  pointer-events: none;
  mix-blend-mode: difference;
  z-index: 9999;
  transform: translate(-50%, -50%);
  transition: transform 0.1s ease;
}

.cursor.active {
  transform: translate(-50%, -50%) scale(1.5);
}

/* Navbar */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 1.5rem 0;
  z-index: 100;
  transition: background-color 0.3s ease, padding 0.3s ease;
}

.navbar.scrolled {
  background-color: rgba(5, 5, 5, 0.9);
  backdrop-filter: blur(10px);
  padding: 1rem 0;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-logo {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(to right, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.navbar-links {
  display: flex;
  gap: 2rem;
}

.navbar-link {
  position: relative;
  font-weight: 500;
  transition: color 0.3s ease;
}

.navbar-link:after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, var(--primary), var(--secondary));
  transition: width 0.3s ease;
}

.navbar-link:hover:after,
.navbar-link.active:after {
  width: 100%;
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1.5rem;
  cursor: pointer;
}

/* Hero Section */
.hero {
  position: relative;
  height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.hero-content {
  max-width: 800px;
  z-index: 1;
}

.hero-title {
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  font-size: clamp(1.2rem, 2vw, 1.8rem);
  margin-bottom: 2.5rem;
  color: var(--text-secondary);
}

.hero-cta {
  display: flex;
  gap: 1.5rem;
}

/* Section styles */
.section {
  padding: 8rem 0;
  position: relative;
}

.section-title {
  margin-bottom: 3rem;
  text-align: center;
}

.section-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* Events section */
.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.event-card {
  position: relative;
  background-color: var(--dark-surface);
  border-radius: 10px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.event-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.2);
}

.event-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.event-content {
  padding: 1.5rem;
}

.event-date {
  display: inline-block;
  background: linear-gradient(to right, var(--primary), var(--secondary));
  color: #000;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.event-title {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.event-description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 6em;
}

/* Clubs section */
.clubs-list {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: center;
  margin-top: 3rem;
}

.club-item {
  flex: 0 0 calc(33.333% - 2rem);
  min-width: 280px;
  background-color: var(--dark-surface);
  border-radius: 10px;
  padding: 2rem;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.club-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.2);
}

.club-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  color: var(--primary);
}

.club-name {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.club-description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 6em;
}

/* About section */
.about-content {
  display: flex;
  gap: 4rem;
  align-items: center;
}

.about-text {
  flex: 1;
}

.about-image {
  flex: 1;
  border-radius: 10px;
  overflow: hidden;
}

.about-image img {
  width: 100%;
  height: auto;
  display: block;
}

/* Footer */
.footer {
  background-color: var(--dark-surface);
  padding: 4rem 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer-logo {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(to right, var(--primary), var(--secondary));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.footer-description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.footer-social-link {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--dark-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.footer-social-link:hover {
  background-color: var(--primary);
}

.footer-heading {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.footer-link {
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: var(--primary);
}

.footer-bottom {
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 768px) {
  .navbar-links {
    display: none;
  }

  .mobile-menu-btn {
    display: block;
  }

  .hero-content {
    text-align: center;
  }

  .hero-cta {
    justify-content: center;
  }

  .about-content {
    flex-direction: column;
  }

  .section {
    padding: 6rem 0;
  }
}

/* Mobile menu */
.mobile-menu {
  position: fixed;
  top: 0;
  right: 0;
  width: 80%;
  max-width: 300px;
  height: 100vh;
  background: linear-gradient(135deg, rgba(17, 17, 17, 0.95), rgba(106, 0, 255, 0.1), rgba(255, 68, 204, 0.1));
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 68, 204, 0.2);
  z-index: 200;
  padding: 2rem;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.mobile-menu.open {
  transform: translateX(0);
}

.mobile-menu-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1.5rem;
  cursor: pointer;
}

.mobile-menu-links {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 3rem;
}

.mobile-menu-link {
  font-size: 1.2rem;
  font-weight: 500;
}

/* Overlay for mobile menu */
.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 199;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.menu-overlay.open {
  opacity: 1;
  pointer-events: auto;
}
